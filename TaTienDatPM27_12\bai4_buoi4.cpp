#include <iostream>
#include <conio.h>
#include <string.h>
using namespace std;

// Lớp Phân Số
class PS {
private:   
    int ts, ms;
    
public:
    PS();
    PS(int, int);
    PS operator+(PS);
    PS operator-(PS);
    PS operator*(PS);
    PS operator/(PS);
    
    friend istream& operator>>(istream& is, PS& ps);
    friend ostream& operator<<(ostream& os, const PS& ps);
};

PS::PS() { 
    ts = 0;   
    ms = 1;     
}

PS::PS(int TS, int MS) { 
    ts = TS;    
    ms = MS;
}

istream& operator>>(istream& is, PS& a) {
    cout << "- Nhap tu so: ";  
    is >> a.ts;
    cout << "- Nhap mau so: "; 
    is >> a.ms;
    return is;
}

ostream& operator<<(ostream& os, const PS& a) {
    os << a.ts << "/" << a.ms;    
    return os;
}

PS PS::operator+(PS ps) {  
    PS Tong;
    Tong.ts = ts * ps.ms + ms * ps.ts;
    Tong.ms = ms * ps.ms;
    return Tong;
}

PS PS::operator-(PS ps) {  
    PS Tru;
    Tru.ts = ts * ps.ms - ms * ps.ts;
    Tru.ms = ms * ps.ms;
    return Tru;
}

PS PS::operator*(PS ps) {
    PS Tich;
    Tich.ts = ts * ps.ts;
    Tich.ms = ms * ps.ms;
    return Tich;
}

PS PS::operator/(PS ps) {
    PS Chia;
    Chia.ts = ts * ps.ms;
    Chia.ms = ms * ps.ts;
    return Chia;
}

// Lớp Sinh Viên
class SinhVien {
private:
    char masv[20], hoten[50], sdt[15];
    float diemTB;
    
public:
    SinhVien();
    SinhVien(const char* ma, const char* ten, const char* dt, float diem);
    float getDiemTB() { return diemTB; }
    
    friend istream& operator>>(istream& is, SinhVien& sv);
    friend ostream& operator<<(ostream& os, SinhVien& sv);
};

SinhVien::SinhVien() {
    strcpy(masv, "");
    strcpy(hoten, "");
    strcpy(sdt, "");
    diemTB = 0.0;
}

SinhVien::SinhVien(const char* ma, const char* ten, const char* dt, float diem) {
    strcpy(masv, ma);
    strcpy(hoten, ten);
    strcpy(sdt, dt);
    diemTB = diem;
}

istream& operator>>(istream& is, SinhVien& sv) {
    cout << "- Nhap ma SV: ";
    cin.ignore();
    cin.getline(sv.masv, 20);
    
    cout << "- Nhap ho ten: ";
    cin.getline(sv.hoten, 50);
    
    cout << "- Nhap so dien thoai: ";
    cin.getline(sv.sdt, 15);
    
    cout << "- Nhap diem trung binh: ";
    is >> sv.diemTB;
    
    return is;
}

ostream& operator<<(ostream& os, SinhVien& sv) {
    os << sv.masv << "\t" << sv.hoten << "\t" << sv.sdt << "\t" << sv.diemTB;
    return os;
}

// Hàm xử lý mảng động phân số
void xuLyPhanSo() {
    int n;
    cout << "\n=== QUAN LY PHAN SO ===" << endl;
    cout << "Nhap so luong phan so: ";
    cin >> n;
    
    // Cấp phát mảng động
    PS* dsPhanSo = new PS[n];
    
    // Nhập dữ liệu
    cout << "\nNhap thong tin " << n << " phan so:" << endl;
    for (int i = 0; i < n; i++) {
        cout << "Phan so thu " << (i + 1) << ":" << endl;
        cin >> dsPhanSo[i];
    }
    
    // Hiển thị danh sách
    cout << "\nDanh sach phan so vua nhap:" << endl;
    for (int i = 0; i < n; i++) {
        cout << "PS[" << i << "] = " << dsPhanSo[i] << endl;
    }
    
    // Tính tổng tất cả phân số
    if (n > 0) {
        PS tong = dsPhanSo[0];
        for (int i = 1; i < n; i++) {
            tong = tong + dsPhanSo[i];
        }
        cout << "\nTong tat ca phan so: " << tong << endl;
    }
    
    // Thực hiện phép tính giữa 2 phân số đầu tiên
    if (n >= 2) {
        cout << "\nCac phep toan voi 2 phan so dau tien:" << endl;
        PS temp1 = dsPhanSo[0] + dsPhanSo[1];
        PS temp2 = dsPhanSo[0] - dsPhanSo[1];
        PS temp3 = dsPhanSo[0] * dsPhanSo[1];
        PS temp4 = dsPhanSo[0] / dsPhanSo[1];
        
        cout << dsPhanSo[0] << " + " << dsPhanSo[1] << " = " << temp1 << endl;
        cout << dsPhanSo[0] << " - " << dsPhanSo[1] << " = " << temp2 << endl;
        cout << dsPhanSo[0] << " * " << dsPhanSo[1] << " = " << temp3 << endl;
        cout << dsPhanSo[0] << " / " << dsPhanSo[1] << " = " << temp4 << endl;
    }
    
    // Giải phóng bộ nhớ
    delete[] dsPhanSo;
}

// Hàm xử lý mảng động sinh viên
void xuLySinhVien() {
    int n;
    cout << "\n=== QUAN LY SINH VIEN ===" << endl;
    cout << "Nhap so luong sinh vien: ";
    cin >> n;
    
    // Cấp phát mảng động
    SinhVien* dsSinhVien = new SinhVien[n];
    
    // Nhập dữ liệu
    cout << "\nNhap thong tin " << n << " sinh vien:" << endl;
    for (int i = 0; i < n; i++) {
        cout << "\nSinh vien thu " << (i + 1) << ":" << endl;
        cin >> dsSinhVien[i];
    }
    
    // Hiển thị danh sách
    cout << "\n=================================================================================" << endl;
    cout << "DANH SACH SINH VIEN" << endl;
    cout << "=================================================================================" << endl;
    cout << "STT\tMa SV\t\tHo Ten\t\t\tSDT\t\tDiem TB" << endl;
    cout << "---------------------------------------------------------------------------------" << endl;
    
    for (int i = 0; i < n; i++) {
        cout << (i + 1) << "\t" << dsSinhVien[i] << endl;
    }
    
    // Tính điểm trung bình chung
    if (n > 0) {
        float tongDiem = 0;
        for (int i = 0; i < n; i++) {
            tongDiem += dsSinhVien[i].getDiemTB();
        }
        cout << "---------------------------------------------------------------------------------" << endl;
        cout << "Diem trung binh cua lop: " << (tongDiem / n) << endl;
    }
    
    // Tìm sinh viên có điểm cao nhất
    if (n > 0) {
        int maxIndex = 0;
        for (int i = 1; i < n; i++) {
            if (dsSinhVien[i].getDiemTB() > dsSinhVien[maxIndex].getDiemTB()) {
                maxIndex = i;
            }
        }
        cout << "Sinh vien co diem cao nhat: " << dsSinhVien[maxIndex] << endl;
    }
    
    cout << "=================================================================================" << endl;
    
    // Giải phóng bộ nhớ
    delete[] dsSinhVien;
}

int main() {
    int luaChon;
    
    do {
        cout << "\n==================================================" << endl;
        cout << "CHUONG TRINH QUAN LY MANG DONG" << endl;
        cout << "==================================================" << endl;
        cout << "1. Quan ly mang phan so" << endl;
        cout << "2. Quan ly mang sinh vien" << endl;
        cout << "3. Thoat chuong trinh" << endl;
        cout << "--------------------------------------------------" << endl;
        cout << "Nhap lua chon (1-3): ";
        cin >> luaChon;
        
        switch (luaChon) {
            case 1:
                xuLyPhanSo();
                break;
            case 2:
                xuLySinhVien();
                break;
            case 3:
                cout << "Cam on ban da su dung chuong trinh!" << endl;
                break;
            default:
                cout << "Lua chon khong hop le!" << endl;
        }
        
        if (luaChon != 3) {
            cout << "\nNhan phim bat ky de tiep tuc...";
            getch();
        }
        
    } while (luaChon != 3);
    
    return 0;
}