#include <iostream>
#include <conio.h>
using namespace std;

class PS {
private:   
    int ts, ms;
    
public:
    PS();
    PS(int, int);
    PS operator+(PS);
    PS operator-(PS);
    PS operator*(PS);
    PS operator/(PS);
    
    friend istream& operator>>(istream& is, PS& ps);
    friend ostream& operator<<(ostream& os, PS& ps);
};

PS::PS() { 
    ts = 0;   
    ms = 1;     
}

PS::PS(int TS, int MS) { 
    ts = TS;    
    ms = MS;
}

istream& operator>>(istream& is, PS& a) {
    cout << "Nhap tu so: ";  
    is >> a.ts;
    cout << "Nhap mau so: "; 
    is >> a.ms;
    return is;
}

ostream& operator<<(ostream& os, PS& a) {
    os << a.ts << "/" << a.ms;    
    return os;
}

PS PS::operator+(PS ps) {  
    PS Tong;
    Tong.ts = ts * ps.ms + ms * ps.ts;
    Tong.ms = ms * ps.ms;
    return Tong;
}

PS PS::operator-(PS ps) {  
    PS Tru;
    Tru.ts = ts * ps.ms - ms * ps.ts;  
    Tru.ms = ms * ps.ms;
    return Tru;
}

PS PS::operator*(PS ps) {
    PS Tich;
    Tich.ts = ts * ps.ts;
    Tich.ms = ms * ps.ms;
    return Tich;
}

PS PS::operator/(PS ps) {
    PS Chia;
    Chia.ts = ts * ps.ms;
    Chia.ms = ms * ps.ts;
    return Chia;  
}

int main() {
    PS a(3, 4), b, c;
    
    cout << "Nhap phan so b:" << endl;    
    cin >> b;
    
    cout << "Phan so khoi tao mac dinh: a = " << a << endl;
    cout << "Phan so vua nhap: b = " << b << endl;
    
    c = a + b;
    cout << "a + b = " << c << endl;
    
    c = a - b;
    cout << "a - b = " << c << endl;
    
    c = a * b;
    cout << "a * b = " << c << endl;
    
    c = a / b;
    cout << "a / b = " << c << endl;
    
    getch();
    return 0;
}