#include <iostream>
#include <string.h>
#include <conio.h>
#include <stdio.h>
using namespace std;

class SinhVien
{
private:
    char masv[20], hoten[20], sdt[15];
    float diemTB;
    
public:
    friend istream& operator>>(istream& is, SinhVien& sv);
    friend ostream& operator<<(ostream& os, SinhVien& sv);
};

istream& operator>>(istream& is, SinhVien& sv)
{
    char tg[20];
    cout << "- Nhap ma SV: ";
    gets(tg);
    strcpy(sv.masv, tg);
    
    cout << "- Nhap ho ten: ";
    gets(tg);
    strcpy(sv.hoten, tg);
    
    cout << "- Nhap so dien thoai: ";
    gets(tg);
    strcpy(sv.sdt, tg);
    
    cout << "- Nhap diem trung binh: ";
    is >> sv.diemTB;
    
    return is;
}

ostream& operator<<(ostream& os, SinhVien& sv)
{
    os << sv.masv << "\t" << sv.hoten << "\t" << sv.sdt << "\t" << sv.diemTB << endl;
    return os;
}

int main()
{
    SinhVien x;
    
    cout << "\nNhap thong tin ve SV:\n";
    cin >> x;
    
    cout << "\nThong tin ve SV vua nhap:\n";
    cout << x;
    
    getch();
    return 0;
}