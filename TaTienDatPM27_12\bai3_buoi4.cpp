#include <iostream>
#include <conio.h>
using namespace std;

class Time {
private: 
    long gio, phut, giay;
    
public:
    Time();
    Time(long h, long m, long s);
    Time operator+(long x);
    friend Time operator+(Time a, Time b);
    long operator-(Time a);
    Time operator++();
    friend ostream& operator<<(ostream& os, Time& t);
    friend istream& operator>>(istream& is, Time& t);
};

Time::Time() {  
    gio = 0; 
    phut = 0; 
    giay = 0; 
}

Time::Time(long h, long m, long s) { 
    gio = h; 
    phut = m; 
    giay = s; 
}

Time Time::operator+(long x) {
    long h;
    h = gio * 3600 + phut * 60 + giay + x; 
    gio = h / 3600;
    phut = (h - gio * 3600) / 60;
    giay = (h - gio * 3600 - phut * 60);
    if (gio >= 24) gio = gio % 24;
    return *this;
}

Time operator+(Time a, Time b) {
    Time Tg;
    Tg.giay = a.giay + b.giay;
    Tg.phut = a.phut + b.phut;
    Tg.gio = a.gio + b.gio;
    
    if (Tg.giay >= 60) {
        Tg.phut++; 
        Tg.giay -= 60;
    }
    if (Tg.phut >= 60) {
        Tg.gio++; 
        Tg.phut -= 60;
    }
    if (Tg.gio >= 24) 
        Tg.gio %= 24;
    
    return Tg;
}

long Time::operator-(Time t) {
    return (gio * 3600 + phut * 60 + giay - t.gio * 3600 - t.phut * 60 - t.giay);
}

Time Time::operator++() {
    long h;
    h = gio * 3600 + phut * 60 + giay + 1;
    gio = h / 3600;
    phut = (h - gio * 3600) / 60;
    giay = (h - gio * 3600 - phut * 60);
    if (gio >= 24) gio = gio % 24;
    return *this;
}

ostream& operator<<(ostream& os, Time& t) {
    os << t.gio << ":" << t.phut << ":" << t.giay;
    return os;
}

istream& operator>>(istream& is, Time& t) {
    cout << "\n - Gio: "; 
    is >> t.gio;
    cout << " - Phut: "; 
    is >> t.phut;
    cout << " - Giay: "; 
    is >> t.giay;
    return is;
}

int main() {
    cout << "\n1. Ham khoi tao:";
    Time t(3, 4, 50); 
    cout << "\nt = " << t << endl;
    
    cout << "\n2. Toan tu nhap >>, xuat <<:";
    Time t1; 
    cin >> t1;
    cout << "t1 = " << t1 << endl;

    long x;
    cout << "\nNhap vao so nguyen x: "; 
    cin >> x;

    cout << "\n3. Cong Time voi mot so nguyen x:";
    Time t2 = t1 + x;
    cout << "\nt2 = t1 + " << x << " = " << t2 << endl;

    cout << "\n4. Cong 2 Time: ";
    cout << "\nNhap t1: "; 
    cin >> t1;
    cout << "Nhap t2: "; 
    cin >> t2;
    Time tt = t1 + t2;
    cout << "t = t1 + t2 = " << tt << endl;

    cout << "\n5. Tru 2 Time: ";
    cout << "\nNhap t1: "; 
    cin >> t1;
    cout << "Nhap t2: "; 
    cin >> t2;
    long h = t1 - t2;
    cout << "h = t1 - t2 = " << h << " giay" << endl;

    cout << "\n6. Toan tu ++";
    Time t4; 
    cout << "\nNhap t4: "; 
    cin >> t4;
    ++t4; 
    cout << "++t4 = " << t4 << endl;
    
    getch(); 
    return 0;
}